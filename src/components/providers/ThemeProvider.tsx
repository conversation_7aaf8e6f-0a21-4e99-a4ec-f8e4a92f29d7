import React, { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useIsDarkTheme, useThemeActions } from '@/lib/store/selectors';
import { useColorScheme as useNativeWindColorScheme } from 'nativewind';
import { getNavTheme } from '@/lib/theme';
import { ThemeProvider as NavThemeProvider } from '@react-navigation/native';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Unified ThemeProvider that synchronizes:
 * 1. Zustand theme state with NativeWind theme system
 * 2. System color scheme detection
 * 3. Navigation theme integration
 * 4. Status bar styling
 *
 * This ensures that both useTheme() and NativeWind classes work correctly
 */
export function ThemeProvider({ children }: Readonly<ThemeProviderProps>) {
  const isDark = useIsDarkTheme();
  const systemColorScheme = useColorScheme();
  const { setColorScheme } = useNativeWindColorScheme();
  const { initializeTheme, setSystemColorScheme } = useThemeActions();

  // Initialize theme system
  useEffect(() => {
    const cleanup = initializeTheme();
    return cleanup;
  }, [initializeTheme]);

  // Sync system color scheme with Zustand
  useEffect(() => {
    setSystemColorScheme(systemColorScheme);
  }, [systemColorScheme, setSystemColorScheme]);

  // Sync Zustand theme state with NativeWind
  useEffect(() => {
    setColorScheme(isDark ? 'dark' : 'light');
  }, [isDark, setColorScheme]);

  const navTheme = getNavTheme(isDark);

  return (
    <NavThemeProvider value={navTheme}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      {children}
    </NavThemeProvider>
  );
}
