import { StateCreator } from "zustand";
import { Appearance, ColorSchemeName } from "react-native";

export type ThemeMode = "light" | "dark" | "system";

export interface ThemeState {
  themeMode: ThemeMode;
  systemColorScheme: ColorSchemeName;
  isDark: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface ThemeActions {
  setThemeMode: (mode: ThemeMode) => void;
  setSystemColorScheme: (colorScheme: ColorSchemeName) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export type ThemeSlice = ThemeState & ThemeActions;

const getCurrentIsDark = (
  themeMode: ThemeMode,
  systemColorScheme: ColorSchemeName,
): boolean => {
  if (themeMode === "system") {
    return systemColorScheme === "dark";
  }

  return themeMode === "dark";
};

export const createThemeSlice: StateCreator<ThemeSlice, [], [], ThemeSlice> = (
  set,
  get,
) => {
  const initialSystemColorScheme = Appearance.getColorScheme();
  const initialIsDark = getCurrentIsDark("system", initialSystemColorScheme);

  return {
    // Initial state
    themeMode: "system",
    systemColorScheme: initialSystemColorScheme,
    isDark: initialIsDark,
    isLoading: false,
    error: null,

    // Actions
    setThemeMode: (mode: ThemeMode) => {
      set((state) => {
        const newIsDark = getCurrentIsDark(mode, state.systemColorScheme);
        return {
          themeMode: mode,
          isDark: newIsDark,
        };
      });
    },

    setSystemColorScheme: (colorScheme: ColorSchemeName) => {
      set((state) => {
        const newIsDark = getCurrentIsDark(state.themeMode, colorScheme);
        return {
          systemColorScheme: colorScheme,
          isDark: newIsDark,
        };
      });
    },

    toggleTheme: () => {
      const { isDark } = get();
      const newMode = isDark ? "light" : "dark";
      set((state) => {
        const newIsDark = getCurrentIsDark(newMode, state.systemColorScheme);
        return {
          themeMode: newMode,
          isDark: newIsDark,
        };
      });
    },

    initializeTheme: () => {
      set({ isLoading: true, error: null });

      try {
        const subscription = Appearance.addChangeListener(({ colorScheme }) => {
          set((state) => {
            const newIsDark = getCurrentIsDark(state.themeMode, colorScheme);
            return {
              isDark: newIsDark,
              systemColorScheme: colorScheme,
            };
          });
        });

        set({ isLoading: false });

        return () => subscription?.remove();
      } catch (error) {
        set({
          error: error instanceof Error
            ? error.message
            : "Failed to initialize theme",
          isLoading: false,
        });
        return () => {};
      }
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    setError: (error: string | null) => {
      set({ error });
    },
  };
};
