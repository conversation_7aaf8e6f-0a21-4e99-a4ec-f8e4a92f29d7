import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

// Import slices
import { createNutritionSlice, NutritionSlice } from "./slices/nutritionSlice";
import {
  createMedicationSlice,
  MedicationSlice,
} from "./slices/medicationSlice";
import { createSettingsSlice, SettingsSlice } from "./slices/settingsSlice";
import { createUISlice, UISlice } from "./slices/uiSlice";
import { createThemeSlice, ThemeSlice } from "./slices/themeSlice";

// Combined store type
export type AppStore =
  & NutritionSlice
  & MedicationSlice
  & SettingsSlice
  & UISlice
  & ThemeSlice;

// Create the main store with simplified middleware
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get, store) => ({
        ...createNutritionSlice(set, get, store),
        ...createMedicationSlice(set, get, store),
        ...createSettingsSlice(set, get, store),
        ...createUISlice(set, get, store),
        ...createThemeSlice(set, get, store),
      }),
      {
        name: "liver-health-store",
        version: 1, // Add versioning for migrations
        partialize: (state) => ({
          // Only persist essential data - exclude large arrays and computed values
          settings: state.settings,
          themeMode: state.themeMode,
          // Only persist recent medications, not all historical data
          medications: state.medications,
          // Limit food logs to last 30 days to prevent storage bloat
          foodLogs: state.foodLogs.filter((log) => {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return log.timestamp >= thirtyDaysAgo;
          }),
        }),
        migrate: (persistedState: any, version: number) => {
          if (version === 0) {
            // Migration logic for version 0 to 1
            return {
              ...persistedState,
              // Add any new required fields with defaults
            };
          }
          return persistedState;
        },
        onRehydrateStorage: () => (state) => {
          if (state) {
            console.log("Store rehydrated successfully");
          }
        },
      },
    ),
    {
      name: "liver-health-store",
      enabled: process.env.NODE_ENV === "development",
    },
  ),
);
