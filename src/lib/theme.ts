import { NAV_THEME } from "./constants";
import { 
  Theme as NavigationTheme,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';

// Unified theme colors - single source of truth
const THEME_COLORS = {
  light: {
    // React Native Reusables colors (HSL)
    reusables: {
      background: "hsl(0 0% 100%)",
      foreground: "hsl(240 10% 3.9%)",
      card: "hsl(0 0% 100%)",
      cardForeground: "hsl(240 10% 3.9%)",
      popover: "hsl(0 0% 100%)",
      popoverForeground: "hsl(240 10% 3.9%)",
      primary: "hsl(240 5.9% 10%)",
      primaryForeground: "hsl(0 0% 98%)",
      secondary: "hsl(240 4.8% 95.9%)",
      secondaryForeground: "hsl(240 5.9% 10%)",
      muted: "hsl(240 4.8% 95.9%)",
      mutedForeground: "hsl(240 3.8% 46.1%)",
      accent: "hsl(240 4.8% 95.9%)",
      accentForeground: "hsl(240 5.9% 10%)",
      destructive: "hsl(0 84.2% 60.2%)",
      destructiveForeground: "hsl(0 0% 98%)",
      border: "hsl(240 5.9% 90%)",
      input: "hsl(240 5.9% 90%)",
      ring: "hsl(240 5.9% 10%)",
    },
    // Legacy app colors (Hex) - for backwards compatibility
    legacy: {
      background: "#F8FAFC",
      surface: "#FFFFFF",
      surfaceSecondary: "#F1F5F9",
      text: "#1F2937",
      textSecondary: "#6B7280",
      primary: "#14B8A6",
      primaryLight: "#5EEAD4",
      success: "#10B981",
      warning: "#F59E0B",
      error: "#EF4444",
      border: "#E5E7EB",
      shadow: "rgba(0, 0, 0, 0.1)",
    },
  },
  dark: {
    // React Native Reusables colors (HSL)
    reusables: {
      background: "hsl(240 10% 3.9%)",
      foreground: "hsl(0 0% 98%)",
      card: "hsl(240 10% 3.9%)",
      cardForeground: "hsl(0 0% 98%)",
      popover: "hsl(240 10% 3.9%)",
      popoverForeground: "hsl(0 0% 98%)",
      primary: "hsl(0 0% 98%)",
      primaryForeground: "hsl(240 5.9% 10%)",
      secondary: "hsl(240 3.7% 15.9%)",
      secondaryForeground: "hsl(0 0% 98%)",
      muted: "hsl(240 3.7% 15.9%)",
      mutedForeground: "hsl(240 5% 64.9%)",
      accent: "hsl(240 3.7% 15.9%)",
      accentForeground: "hsl(0 0% 98%)",
      destructive: "hsl(0 62.8% 30.6%)",
      destructiveForeground: "hsl(0 0% 98%)",
      border: "hsl(240 3.7% 15.9%)",
      input: "hsl(240 3.7% 15.9%)",
      ring: "hsl(240 4.9% 83.9%)",
    },
    // Legacy app colors (Hex) - for backwards compatibility
    legacy: {
      background: "#0F172A",
      surface: "#1E293B",
      surfaceSecondary: "#334155",
      text: "#F8FAFC",
      textSecondary: "#CBD5E1",
      primary: "#14B8A6",
      primaryLight: "#5EEAD4",
      success: "#10B981",
      warning: "#F59E0B",
      error: "#EF4444",
      border: "#475569",
      shadow: "rgba(0, 0, 0, 0.3)",
    },
  },
} as const;

// React Native Reusables theme objects
export const LIGHT_THEME = {
  dark: false,
  colors: THEME_COLORS.light.reusables,
};

export const DARK_THEME = {
  dark: true,
  colors: THEME_COLORS.dark.reusables,
};

// Navigation theme objects
export const LIGHT_NAV_THEME: NavigationTheme = {
  ...DefaultTheme,
  colors: NAV_THEME.light,
};

export const DARK_NAV_THEME: NavigationTheme = {
  ...DarkTheme,
  colors: NAV_THEME.dark,
};

// Theme getter functions
export function getTheme(isDark: boolean) {
  return isDark ? DARK_THEME : LIGHT_THEME;
}

export function getNavTheme(isDark: boolean) {
  return isDark ? DARK_NAV_THEME : LIGHT_NAV_THEME;
}

export function getLegacyColors(isDark: boolean) {
  return isDark ? THEME_COLORS.dark.legacy : THEME_COLORS.light.legacy;
}

// Type exports
export type ThemeColors = typeof THEME_COLORS.light.reusables;
export type LegacyColors = typeof THEME_COLORS.light.legacy;
