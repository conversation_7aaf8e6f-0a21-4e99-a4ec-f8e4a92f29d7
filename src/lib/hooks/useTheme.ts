import { useIsDarkTheme, useThemeActions } from "@/lib/store/selectors";
import { getLegacyColors, getTheme, type LegacyColors } from "@/lib/theme";

/**
 * Unified theme hook - provides both react-native-reusables and legacy colors
 *
 * @returns Object with theme state, colors, and actions
 *
 * @example
 * // For new components using react-native-reusables
 * const { reusableColors } = useTheme();
 *
 * @example
 * // For legacy components that need hex colors
 * const { legacyColors } = useTheme();
 *
 * @example
 * // For theme actions
 * const { toggleTheme, setThemeMode } = useTheme();
 */
export function useTheme() {
  const isDark = useIsDarkTheme();
  const themeActions = useThemeActions();

  const reusableTheme = getTheme(isDark);
  const legacyColors = getLegacyColors(isDark);

  return {
    // Theme state
    isDark,

    // Color systems
    legacyColors,
    reusableColors: reusableTheme.colors,

    // For backwards compatibility - use legacyColors instead
    colors: legacyColors,

    // Theme actions
    ...themeActions,
  };
}

/**
 * Get a theme color programmatically (for edge cases only)
 * Most components should use Tailwind classes instead
 *
 * @deprecated Use useTheme().legacyColors directly instead
 */
export function getThemeColor(
  colorName: keyof LegacyColors,
  isDark: boolean,
): string {
  const colors = getLegacyColors(isDark);
  const color = colors[colorName];

  if (!color) {
    console.warn(
      `Unknown color: ${colorName}. Available colors: ${
        Object.keys(colors).join(", ")
      }`,
    );
    return "#000000";
  }

  return color;
}
