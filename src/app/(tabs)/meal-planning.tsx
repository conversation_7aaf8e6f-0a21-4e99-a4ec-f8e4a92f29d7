import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Modal } from 'react-native';
import {
  Plus,
  ChefHat,
  TriangleAlert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CircleCheck as CheckCircle,
  Clock,
  Users,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface MealPlan {
  id: string;
  name: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  ingredients: string[];
  instructions: string[];
  nutrition: {
    sodium: number;
    protein: number;
    fat: number;
    calories: number;
  };
  isLiverFriendly: boolean;
  prepTime: number;
  servings: number;
  warnings: string[];
}

interface WeeklyPlan {
  [key: string]: {
    [mealType: string]: MealPlan | null;
  };
}

export default function MealPlanningScreen() {
  const [selectedDay, setSelectedDay] = useState('Monday');
  const [showAddMeal, setShowAddMeal] = useState(false);
  const [selectedMealType, setSelectedMealType] = useState<
    'breakfast' | 'lunch' | 'dinner' | 'snack'
  >('breakfast');
  const [weeklyPlan, setWeeklyPlan] = useState<WeeklyPlan>({});

  const daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

  // Sample liver-friendly meal plans
  const sampleMealPlans: MealPlan[] = [
    {
      id: '1',
      name: 'Grilled Chicken with Steamed Vegetables',
      mealType: 'lunch',
      ingredients: [
        '150g chicken breast',
        '1 cup broccoli',
        '1 cup carrots',
        '1 tbsp olive oil',
        'Fresh herbs (rosemary, thyme)',
      ],
      instructions: [
        'Season chicken breast with herbs',
        'Grill chicken for 6-7 minutes each side',
        'Steam vegetables until tender',
        'Drizzle with olive oil and serve',
      ],
      nutrition: {
        sodium: 125,
        protein: 35,
        fat: 8,
        calories: 285,
      },
      isLiverFriendly: true,
      prepTime: 25,
      servings: 1,
      warnings: [],
    },
    {
      id: '2',
      name: 'Oatmeal with Fresh Berries',
      mealType: 'breakfast',
      ingredients: [
        '1/2 cup rolled oats',
        '1 cup low-fat milk',
        '1/2 cup mixed berries',
        '1 tsp honey',
        '1 tbsp chopped walnuts',
      ],
      instructions: [
        'Cook oats with milk until creamy',
        'Top with fresh berries',
        'Drizzle with honey',
        'Sprinkle with chopped walnuts',
      ],
      nutrition: {
        sodium: 85,
        protein: 12,
        fat: 6,
        calories: 245,
      },
      isLiverFriendly: true,
      prepTime: 10,
      servings: 1,
      warnings: [],
    },
    {
      id: '3',
      name: 'Baked Salmon with Quinoa',
      mealType: 'dinner',
      ingredients: [
        '120g salmon fillet',
        '1/2 cup quinoa',
        '1 cup spinach',
        '1 tbsp lemon juice',
        '1 tsp olive oil',
      ],
      instructions: [
        'Bake salmon at 375°F for 15 minutes',
        'Cook quinoa according to package directions',
        'Sauté spinach until wilted',
        'Serve salmon over quinoa and spinach',
        'Drizzle with lemon juice',
      ],
      nutrition: {
        sodium: 95,
        protein: 28,
        fat: 12,
        calories: 320,
      },
      isLiverFriendly: true,
      prepTime: 30,
      servings: 1,
      warnings: [],
    },
    {
      id: '4',
      name: 'Processed Deli Sandwich',
      mealType: 'lunch',
      ingredients: [
        '2 slices white bread',
        '100g processed deli meat',
        '2 slices processed cheese',
        '1 tbsp mayo',
      ],
      instructions: [
        'Layer meat and cheese on bread',
        'Add mayo and close sandwich',
      ],
      nutrition: {
        sodium: 1450,
        protein: 25,
        fat: 18,
        calories: 420,
      },
      isLiverFriendly: false,
      prepTime: 5,
      servings: 1,
      warnings: [
        'Very high sodium content',
        'Processed meats may worsen liver inflammation',
      ],
    },
  ];

  const addMealToPlan = (meal: MealPlan) => {
    setWeeklyPlan(prev => ({
      ...prev,
      [selectedDay]: {
        ...prev[selectedDay],
        [selectedMealType]: meal,
      },
    }));
    setShowAddMeal(false);
  };

  const removeMealFromPlan = (day: string, mealType: string) => {
    setWeeklyPlan(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [mealType]: null,
      },
    }));
  };

  const getDayNutrition = (day: string) => {
    const dayPlan = weeklyPlan[day] || {};
    return Object.values(dayPlan).reduce(
      (total, meal) => {
        if (meal) {
          return {
            sodium: total.sodium + meal.nutrition.sodium,
            protein: total.protein + meal.nutrition.protein,
            fat: total.fat + meal.nutrition.fat,
            calories: total.calories + meal.nutrition.calories,
          };
        }
        return total;
      },
      { sodium: 0, protein: 0, fat: 0, calories: 0 }
    );
  };

  const todayNutrition = getDayNutrition(selectedDay);

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="px-5 pt-4">
          <Text className="text-3xl font-bold text-foreground">
            Meal Planning
          </Text>
          <Text className="text-base text-muted-foreground mt-1">
            Plan liver-friendly meals
          </Text>
        </View>

        {/* Day Selector */}
        <View className="px-5 mt-6">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {daysOfWeek.map(day => (
              <TouchableOpacity
                key={day}
                className={`px-4 py-2 mr-3 rounded-lg border ${
                  selectedDay === day
                    ? 'bg-primary border-primary'
                    : 'bg-card border-border'
                }`}
                onPress={() => setSelectedDay(day)}
              >
                <Text
                  className={`text-sm font-medium ${
                    selectedDay === day
                      ? 'text-primary-foreground'
                      : 'text-muted-foreground'
                  }`}
                >
                  {day.slice(0, 3)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Daily Nutrition Summary */}
        <View className="mx-5 mt-6 p-4 bg-card rounded-xl">
          <Text className="text-lg font-semibold text-foreground mb-4">
            {selectedDay} Nutrition
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text
                className={`text-lg font-bold ${
                  todayNutrition.sodium > 2000
                    ? 'text-destructive'
                    : 'text-success'
                }`}
              >
                {todayNutrition.sodium}mg
              </Text>
              <Text className="text-sm text-muted-foreground">Sodium</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-primary">
                {todayNutrition.protein}g
              </Text>
              <Text className="text-sm text-muted-foreground">Protein</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-warning">
                {todayNutrition.fat}g
              </Text>
              <Text className="text-sm text-muted-foreground">Fat</Text>
            </View>
            <View className="items-center">
              <Text className="text-lg font-bold text-info">
                {todayNutrition.calories}
              </Text>
              <Text className="text-sm text-muted-foreground">Calories</Text>
            </View>
          </View>
        </View>

        {/* Meal Plan for Selected Day */}
        <View style={styles.mealPlanSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Meals for {selectedDay}
          </Text>
          {mealTypes.map(mealType => {
            const meal = weeklyPlan[selectedDay]?.[mealType];
            return (
              <View key={mealType} style={styles.mealSlot}>
                <View style={styles.mealSlotHeader}>
                  <View style={styles.mealTypeInfo}>
                    <ChefHat size={20} color={colors.primary} />
                    <Text
                      style={[styles.mealTypeTitle, { color: colors.text }]}
                    >
                      {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={[
                      styles.addMealButton,
                      {
                        backgroundColor: colors.surface,
                        borderColor: colors.primary,
                      },
                    ]}
                    onPress={() => {
                      setSelectedMealType(mealType as any);
                      setShowAddMeal(true);
                    }}
                  >
                    <Plus size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>

                {meal ? (
                  <TouchableOpacity
                    style={[
                      styles.mealCard,
                      { backgroundColor: colors.surface },
                    ]}
                    onLongPress={() =>
                      removeMealFromPlan(selectedDay, mealType)
                    }
                  >
                    <View style={styles.mealCardHeader}>
                      <Text style={[styles.mealName, { color: colors.text }]}>
                        {meal.name}
                      </Text>
                      <View style={styles.mealStatus}>
                        {meal.isLiverFriendly ? (
                          <CheckCircle size={16} color="#10B981" />
                        ) : (
                          <AlertTriangle size={16} color="#EF4444" />
                        )}
                      </View>
                    </View>

                    <View style={styles.mealDetails}>
                      <View style={styles.mealDetailItem}>
                        <Clock size={14} color={colors.textSecondary} />
                        <Text
                          style={[
                            styles.mealDetailText,
                            { color: colors.textSecondary },
                          ]}
                        >
                          {meal.prepTime} min
                        </Text>
                      </View>
                      <View style={styles.mealDetailItem}>
                        <Users size={14} color={colors.textSecondary} />
                        <Text
                          style={[
                            styles.mealDetailText,
                            { color: colors.textSecondary },
                          ]}
                        >
                          {meal.servings} serving
                        </Text>
                      </View>
                    </View>

                    <View style={styles.mealNutrition}>
                      <Text
                        style={[
                          styles.mealNutritionText,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {meal.nutrition.sodium}mg sodium •{' '}
                        {meal.nutrition.protein}g protein •{' '}
                        {meal.nutrition.calories} cal
                      </Text>
                    </View>

                    {meal.warnings.length > 0 && (
                      <View
                        style={[
                          styles.warningContainer,
                          {
                            backgroundColor: isDark
                              ? 'rgba(239, 68, 68, 0.1)'
                              : '#FEF2F2',
                          },
                        ]}
                      >
                        <AlertTriangle size={14} color="#EF4444" />
                        <Text
                          style={[styles.warningText, { color: colors.error }]}
                        >
                          {meal.warnings[0]}
                        </Text>
                      </View>
                    )}
                  </TouchableOpacity>
                ) : (
                  <View
                    style={[
                      styles.emptyMealSlot,
                      {
                        backgroundColor: colors.surfaceSecondary,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.emptyMealText,
                        { color: colors.textTertiary },
                      ]}
                    >
                      No meal planned
                    </Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>

        {/* Weekly Overview */}
        <View style={styles.weeklyOverview}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Weekly Overview
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {daysOfWeek.map(day => {
              const dayNutrition = getDayNutrition(day);
              const plannedMeals = Object.values(weeklyPlan[day] || {}).filter(
                Boolean
              ).length;

              return (
                <View
                  key={day}
                  style={[
                    styles.weeklyDayCard,
                    { backgroundColor: colors.surface },
                  ]}
                >
                  <Text style={[styles.weeklyDayName, { color: colors.text }]}>
                    {day.slice(0, 3)}
                  </Text>
                  <Text
                    style={[
                      styles.weeklyMealCount,
                      { color: colors.textSecondary },
                    ]}
                  >
                    {plannedMeals}/4 meals
                  </Text>
                  <Text
                    style={[
                      styles.weeklySodium,
                      {
                        color:
                          dayNutrition.sodium > 2000 ? '#EF4444' : '#10B981',
                      },
                    ]}
                  >
                    {dayNutrition.sodium}mg Na
                  </Text>
                </View>
              );
            })}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Add Meal Modal */}
      <Modal visible={showAddMeal} animationType="slide">
        <SafeAreaView
          style={[
            styles.modalContainer,
            { backgroundColor: colors.background },
          ]}
        >
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add{' '}
              {selectedMealType.charAt(0).toUpperCase() +
                selectedMealType.slice(1)}
            </Text>
            <TouchableOpacity onPress={() => setShowAddMeal(false)}>
              <Text style={[styles.modalClose, { color: colors.primary }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.mealOptionsContainer}>
            {sampleMealPlans
              .filter(meal => meal.mealType === selectedMealType)
              .map(meal => (
                <TouchableOpacity
                  key={meal.id}
                  style={[
                    styles.mealOption,
                    { backgroundColor: colors.surface },
                  ]}
                  onPress={() => addMealToPlan(meal)}
                >
                  <View style={styles.mealOptionHeader}>
                    <Text
                      style={[styles.mealOptionName, { color: colors.text }]}
                    >
                      {meal.name}
                    </Text>
                    <View style={styles.mealOptionStatus}>
                      {meal.isLiverFriendly ? (
                        <CheckCircle size={20} color="#10B981" />
                      ) : (
                        <AlertTriangle size={20} color="#EF4444" />
                      )}
                    </View>
                  </View>

                  <View style={styles.mealOptionDetails}>
                    <View style={styles.mealOptionDetailItem}>
                      <Clock size={14} color={colors.textSecondary} />
                      <Text
                        style={[
                          styles.mealOptionDetailText,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {meal.prepTime} min
                      </Text>
                    </View>
                    <View style={styles.mealOptionDetailItem}>
                      <Users size={14} color={colors.textSecondary} />
                      <Text
                        style={[
                          styles.mealOptionDetailText,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {meal.servings} serving
                      </Text>
                    </View>
                  </View>

                  <View style={styles.mealOptionNutrition}>
                    <Text
                      style={[
                        styles.mealOptionNutritionText,
                        { color: colors.textSecondary },
                      ]}
                    >
                      {meal.nutrition.sodium}mg sodium •{' '}
                      {meal.nutrition.protein}g protein •{' '}
                      {meal.nutrition.calories} cal
                    </Text>
                  </View>

                  <View style={styles.ingredientsList}>
                    <Text
                      style={[styles.ingredientsTitle, { color: colors.text }]}
                    >
                      Ingredients:
                    </Text>
                    {meal.ingredients.slice(0, 3).map(ingredient => (
                      <Text
                        key={ingredient}
                        style={[
                          styles.ingredientItem,
                          { color: colors.textSecondary },
                        ]}
                      >
                        • {ingredient}
                      </Text>
                    ))}
                    {meal.ingredients.length > 3 && (
                      <Text
                        style={[
                          styles.ingredientMore,
                          { color: colors.textTertiary },
                        ]}
                      >
                        +{meal.ingredients.length - 3} more ingredients
                      </Text>
                    )}
                  </View>

                  {meal.warnings.length > 0 && (
                    <View
                      style={[
                        styles.warningContainer,
                        {
                          backgroundColor: isDark
                            ? 'rgba(239, 68, 68, 0.1)'
                            : '#FEF2F2',
                        },
                      ]}
                    >
                      <AlertTriangle size={14} color="#EF4444" />
                      <Text
                        style={[styles.warningText, { color: colors.error }]}
                      >
                        {meal.warnings[0]}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  daySelector: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  dayButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  dayButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  nutritionSummary: {
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 16,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontFamily: 'Inter-Bold',
    fontSize: 18,
    marginBottom: 4,
  },
  nutritionLabel: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  mealPlanSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 20,
    marginBottom: 16,
  },
  mealSlot: {
    marginBottom: 16,
  },
  mealSlotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  mealTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mealTypeTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
  addMealButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mealCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  mealCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  mealName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    flex: 1,
  },
  mealStatus: {
    marginLeft: 8,
  },
  mealDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  mealDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  mealDetailText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 4,
  },
  mealNutrition: {
    marginBottom: 8,
  },
  mealNutritionText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  emptyMealSlot: {
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  emptyMealText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  weeklyOverview: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  weeklyDayCard: {
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    minWidth: 80,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  weeklyDayName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    marginBottom: 4,
  },
  weeklyMealCount: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginBottom: 4,
  },
  weeklySodium: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  warningText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
  },
  modalClose: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  mealOptionsContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  mealOption: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mealOptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  mealOptionName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    flex: 1,
  },
  mealOptionStatus: {
    marginLeft: 8,
  },
  mealOptionDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  mealOptionDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  mealOptionDetailText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 4,
  },
  mealOptionNutrition: {
    marginBottom: 12,
  },
  mealOptionNutritionText: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  ingredientsList: {
    marginBottom: 8,
  },
  ingredientsTitle: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginBottom: 4,
  },
  ingredientItem: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginBottom: 2,
  },
  ingredientMore: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    fontStyle: 'italic',
  },
});
